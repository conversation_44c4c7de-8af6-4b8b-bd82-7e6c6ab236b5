variables:
  ROOT_PATH: ./rich-shelves-fe/rich-shelves-com

cache:
  paths:
    - $ROOT_PATH/node_modules/

build-job:
  stage: build
  image: node:24
  script:
    - echo 'ROOT_PATH=$ROOT_PATH'
    - cd $ROOT_PATH
    - pwd
    - ls -la
    - rm -rf dist || true
    - mkdir dist
    - cp index.html dist
    - cp 404.html dist
  artifacts:
    paths:
      - $ROOT_PATH/dist/
    expire_in: 90 days

deploy-job:
  stage: deploy
  image: node:24
  needs: ["build-job"]
  variables:
    CLOUDFLARE_API_TOKEN: $RS_COM_CLOUDFLARE_API_TOKEN
    CLOUDFLARE_ACCOUNT_ID: $RS_COM_CLOUDFLARE_ACCOUNT_ID
  script:
    - echo 'ROOT_PATH=$ROOT_PATH'
    - cd $ROOT_PATH
    - pwd
    - ls -la .
    - ls -la ./dist
    - npm install -g wrangler
    - wrangler deploy
  when: manual
  only:
    - main
