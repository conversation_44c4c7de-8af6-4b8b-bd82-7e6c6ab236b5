export default {
    async fetch(request, env, ctx) {
        let url = new URL(request.url);
        let path = url.pathname;

        // Rewrite "/xyz" to "/xyz.html" unless root
        if (path !== "/" && !path.endsWith(".html") && !path.includes(".")) {
            path = `${path}.html`;
            url.pathname = path;
            request = new Request(url, request);
        }

        // Serve static asset
        let response = await env.ASSETS.fetch(request);

        // Custom 404
        if (response.status === 404) {
            let notFoundRequest = new Request(new URL('/404.html', url), request);
            let notFoundResp = await env.ASSETS.fetch(notFoundRequest);
            return new Response(notFoundResp.body, {
                status: 404,
                headers: notFoundResp.headers
            });
        }

        return response;
    }
};
